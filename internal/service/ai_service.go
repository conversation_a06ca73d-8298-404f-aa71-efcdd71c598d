package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"strings"
	"time"
)

type AIService struct {
	modelConfigRepo *repository.ModelConfigRepository
	aiLogService    *AILogService
}

// NewAIService 创建AI服务实例
func NewAIService(modelConfigRepo *repository.ModelConfigRepository, aiLogService *AILogService) *AIService {
	return &AIService{
		modelConfigRepo: modelConfigRepo,
		aiLogService:    aiLogService,
	}
}

// QwenVLResult Qwen-VL调用结果
type QwenVLResult struct {
	Structure   *model.QuestionStructure
	RawContent  string // 原始content字符串
	LogID       string // 日志ID
}

// DeepseekResult Deepseek调用结果
type DeepseekResult struct {
	Analysis   string
	Answer     string
	RawContent string // 原始content字符串
}

// DeepSeek请求结构体定义（使用标准OpenAI格式）
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponseFormat struct {
	Type string `json:"type"`
}

type DeepSeekRequest struct {
	Model          string                   `json:"model"`
	Messages       []DeepSeekMessage        `json:"messages"`
	Temperature    *float64                 `json:"temperature,omitempty"`
	TopP           *float64                 `json:"top_p,omitempty"`
	TopK           *int                     `json:"top_k,omitempty"`
	MaxTokens      *int                     `json:"max_tokens,omitempty"`
	DoSample       *bool                    `json:"do_sample,omitempty"`
	ResponseFormat *DeepSeekResponseFormat  `json:"response_format,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	Stream         *bool                    `json:"stream,omitempty"`
	Stop           interface{}              `json:"stop,omitempty"`
}

// CallQwenVL 调用Qwen-VL模型进行图像识别
func (s *AIService) CallQwenVL(imageURL string) (*QwenVLResult, error) {
	// 创建日志条目
	var logID string
	if s.aiLogService != nil {
		logID = s.aiLogService.CreateLogEntry()
	}
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("获取Qwen-VL模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Qwen-VL模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 从参数中获取动态配置的消息内容
	systemMessage := "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}"
	userMessage := "question_text内的值不应该出现题目类型以及问题序号。"

	// 如果参数中有自定义消息，则使用自定义消息
	if sysMsg, exists := params["system_message"]; exists {
		if sysStr, ok := sysMsg.(string); ok && sysStr != "" {
			systemMessage = sysStr
		}
	}
	if usrMsg, exists := params["user_message"]; exists {
		if usrStr, ok := usrMsg.(string); ok && usrStr != "" {
			userMessage = usrStr
		}
	}

	// 获取模型名称
	modelName := "qwen-vl-plus" // 默认值
	if model, exists := params["model"]; exists {
		if modelStr, ok := model.(string); ok && modelStr != "" {
			modelName = modelStr
		}
	}

	// 添加Qwen API支持的技术约束参数（放在parameters对象中）
	// 严格按照Qwen API文档，只添加支持的技术参数
	parameters := make(map[string]interface{})
	supportedParams := []string{
		"temperature", "top_p", "top_k", "do_sample",
		"response_format", "frequency_penalty", "presence_penalty",
		"result_format",
	}

	for _, paramName := range supportedParams {
		if value, exists := params[paramName]; exists {
			parameters[paramName] = value
		}
	}

	// 确保result_format为message（DashScope格式要求）
	parameters["result_format"] = "message"

	// 定义消息结构体以确保字段顺序
	type MessageContent struct {
		Image string `json:"image,omitempty"`
		Text  string `json:"text,omitempty"`
	}

	type Message struct {
		Role    string           `json:"role"`
		Content interface{}      `json:"content"`
	}

	type InputData struct {
		Messages []Message `json:"messages"`
	}

	type RequestBody struct {
		Model      string      `json:"model"`
		Parameters interface{} `json:"parameters"`
		Input      InputData   `json:"input"`
	}

	// 构建符合Qwen API DashScope格式的请求体
	// 按照要求的顺序：1.model 2.parameters 3.input.messages
	requestBody := RequestBody{
		Model:      modelName,
		Parameters: parameters,
		Input: InputData{
			Messages: []Message{
				{
					Role:    "system",
					Content: systemMessage, // 取数据库内配置的system_message
				},
				{
					Role: "user",
					Content: []MessageContent{
						{
							Image: imageURL, // 取用户在业务中提交的图片url
						},
						{
							Text: userMessage, // 取数据库内配置的user_message
						},
					},
				},
			},
		},
	}

	// 记录请求数据到日志
	if s.aiLogService != nil {
		// 直接使用结构体保持字段顺序
		s.aiLogService.LogQwenRequest(logID, requestBody)
	}

	// 打印发送给Qwen的原始请求数据用于调试
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给Qwen的原始请求数据:\n%s\n", string(requestBytes))

	// 临时硬编码正确的DashScope多模态API URL进行测试
	correctAPIURL := "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

	// 发送请求
	response, err := s.sendRequest(correctAPIURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Qwen-VL模型失败: %w", err)
	}

	// 解析响应
	var qwenResponse model.QwenVLResponse
	if err := json.Unmarshal(response, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析Qwen-VL响应失败: %w", err)
	}

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("Qwen-VL模型返回空结果")
	}

	// 打印Qwen原始content数据用于调试
	rawContent := qwenResponse.Output.Choices[0].Message.Content
	contentBytes, _ := json.Marshal(rawContent)
	fmt.Printf("🔍 Qwen原始Content数据: %s\n", string(contentBytes))

	// 使用新的方法提取内容字符串
	content := qwenResponse.GetContentString()

	// 记录响应数据到日志
	if s.aiLogService != nil {
		s.aiLogService.LogQwenResponse(logID, content)
	}
	if content == "" {
		return nil, fmt.Errorf("Qwen-VL模型返回空内容")
	}

	// 尝试解析JSON格式的响应
	structure, err := s.parseQwenResponse(content)
	if err != nil {
		return nil, fmt.Errorf("解析Qwen响应失败: %w", err)
	}

	return &QwenVLResult{
		Structure:  &structure,
		RawContent: content,
		LogID:      logID,
	}, nil
}

// CallDeepseek 调用Deepseek模型进行题目解析
func (s *AIService) CallDeepseek(qwenResult *QwenVLResult) (*DeepseekResult, error) {
	return s.CallDeepseekWithLogID(qwenResult, "")
}

// CallDeepseekWithLogID 调用Deepseek模型进行题目解析（带日志ID）
func (s *AIService) CallDeepseekWithLogID(qwenResult *QwenVLResult, logID string) (*DeepseekResult, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameDeepseekChat)
	if err != nil {
		return nil, fmt.Errorf("获取Deepseek模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Deepseek模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 获取配置的消息内容
	systemMessage, _ := params["system_message"].(string)
	if systemMessage == "" {
		systemMessage = "你是一个专业的题目解析助手，请对提供的题目进行详细解析。"
	}

	userMessage, _ := params["user_message"].(string)
	if userMessage == "" {
		userMessage = "请以JSON格式返回，包含analysis（详细解题思路）和answer（最终答案）字段。"
	}

	// 构建完整的用户消息，只使用Qwen的原始响应内容，避免重复
	// qwenResult.RawContent 已经包含了完整的JSON数据，无需重复添加Structure
	fullUserMessage := fmt.Sprintf(`%s

以下是图像识别系统提取的题目信息：
%s

请基于以上信息进行详细的题目解析。`, userMessage, qwenResult.RawContent)

	// 构建标准OpenAI格式的请求体
	requestBody := DeepSeekRequest{
		Model: modelConfig.Name,
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemMessage,
			},
			{
				Role:    "user",
				Content: fullUserMessage,
			},
		},
	}

	// 严格按照数据库配置的参数执行，只添加存在于配置中的参数
	fmt.Printf("🔍 DeepSeek配置参数: %+v\n", params)

	// 支持的DeepSeek API参数列表
	supportedParams := map[string]bool{
		"temperature":        true,
		"top_p":             true,
		"top_k":             true,
		"max_tokens":        true,
		"do_sample":         true,
		"response_format":   true,
		"frequency_penalty": true,
		"presence_penalty":  true,
		"stream":            true,
		"stop":              true,
	}

	// 只处理数据库中配置的且API支持的参数
	if temp, exists := params["temperature"]; exists && supportedParams["temperature"] {
		if tempFloat, ok := temp.(float64); ok {
			requestBody.Temperature = &tempFloat
			fmt.Printf("✅ 添加temperature参数: %f\n", tempFloat)
		}
	}

	if topP, exists := params["top_p"]; exists && supportedParams["top_p"] {
		if topPFloat, ok := topP.(float64); ok {
			requestBody.TopP = &topPFloat
			fmt.Printf("✅ 添加top_p参数: %f\n", topPFloat)
		}
	}

	if topK, exists := params["top_k"]; exists && supportedParams["top_k"] {
		if topKFloat, ok := topK.(float64); ok {
			topKInt := int(topKFloat)
			requestBody.TopK = &topKInt
			fmt.Printf("✅ 添加top_k参数: %d\n", topKInt)
		} else if topKInt, ok := topK.(int); ok {
			requestBody.TopK = &topKInt
			fmt.Printf("✅ 添加top_k参数: %d\n", topKInt)
		}
	}

	if maxTokens, exists := params["max_tokens"]; exists && supportedParams["max_tokens"] {
		if maxTokensFloat, ok := maxTokens.(float64); ok {
			maxTokensInt := int(maxTokensFloat)
			requestBody.MaxTokens = &maxTokensInt
			fmt.Printf("✅ 添加max_tokens参数: %d\n", maxTokensInt)
		} else if maxTokensInt, ok := maxTokens.(int); ok {
			requestBody.MaxTokens = &maxTokensInt
			fmt.Printf("✅ 添加max_tokens参数: %d\n", maxTokensInt)
		}
	}

	if doSample, exists := params["do_sample"]; exists && supportedParams["do_sample"] {
		if doSampleBool, ok := doSample.(bool); ok {
			requestBody.DoSample = &doSampleBool
			fmt.Printf("✅ 添加do_sample参数: %v\n", doSampleBool)
		}
	}

	// 处理response_format参数
	if responseFormat, exists := params["response_format"]; exists && supportedParams["response_format"] {
		if responseFormatMap, ok := responseFormat.(map[string]interface{}); ok {
			if formatType, typeExists := responseFormatMap["type"].(string); typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
				fmt.Printf("✅ 添加response_format参数: %s\n", formatType)
			}
		} else if responseFormatMap, ok := responseFormat.(map[string]string); ok {
			if formatType, typeExists := responseFormatMap["type"]; typeExists {
				requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
				fmt.Printf("✅ 添加response_format参数: %s\n", formatType)
			}
		}
	}

	// 只有在数据库配置中存在时才添加惩罚参数
	if freqPenalty, exists := params["frequency_penalty"]; exists && supportedParams["frequency_penalty"] {
		if freqPenaltyFloat, ok := freqPenalty.(float64); ok {
			requestBody.FrequencyPenalty = &freqPenaltyFloat
			fmt.Printf("✅ 添加frequency_penalty参数: %f\n", freqPenaltyFloat)
		}
	}

	if presPenalty, exists := params["presence_penalty"]; exists && supportedParams["presence_penalty"] {
		if presPenaltyFloat, ok := presPenalty.(float64); ok {
			requestBody.PresencePenalty = &presPenaltyFloat
			fmt.Printf("✅ 添加presence_penalty参数: %f\n", presPenaltyFloat)
		}
	}

	// 处理stream参数
	if stream, exists := params["stream"]; exists && supportedParams["stream"] {
		if streamBool, ok := stream.(bool); ok {
			requestBody.Stream = &streamBool
			fmt.Printf("✅ 添加stream参数: %v\n", streamBool)
		}
	}

	// 处理stop参数
	if stop, exists := params["stop"]; exists && supportedParams["stop"] {
		requestBody.Stop = stop
		fmt.Printf("✅ 添加stop参数: %+v\n", stop)
	}

	// 记录请求数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekRequest(logID, requestBody)
	}

	// 打印发送给DeepSeek的原始请求数据用于调试
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给DeepSeek的原始请求数据:\n%s\n", string(requestBytes))

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Deepseek模型失败: %w", err)
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(response, &deepseekResponse); err != nil {
		return nil, fmt.Errorf("解析Deepseek响应失败: %w", err)
	}

	if len(deepseekResponse.Choices) == 0 {
		return nil, fmt.Errorf("Deepseek模型返回空结果")
	}

	content := deepseekResponse.Choices[0].Message.Content

	// 打印DeepSeek原始Content数据用于调试
	fmt.Printf("🔍 DeepSeek原始Content数据: %s\n", content)

	// 记录响应数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekResponse(logID, content)
	}

	// 验证响应内容是否有效
	if err := s.validateDeepSeekResponse(content); err != nil {
		return nil, fmt.Errorf("DeepSeek响应内容无效: %w", err)
	}

	// 尝试解析JSON格式的响应
	var result struct {
		Analysis string `json:"analysis"`
		Answer   string `json:"answer"`
	}

	if err := json.Unmarshal([]byte(content), &result); err != nil {
		// 如果不是JSON格式，检查是否为有效的纯文本响应
		if len(strings.TrimSpace(content)) < 10 {
			return nil, fmt.Errorf("DeepSeek返回内容过短，可能是错误响应: %s", content)
		}

		// 检查是否包含错误关键词
		errorKeywords := []string{"error", "错误", "失败", "无法", "不能", "sorry", "apologize"}
		contentLower := strings.ToLower(content)
		for _, keyword := range errorKeywords {
			if strings.Contains(contentLower, keyword) {
				return nil, fmt.Errorf("DeepSeek返回错误信息: %s", content)
			}
		}

		// 作为纯文本处理，但需要有足够的内容
		return &DeepseekResult{
			Analysis:   content,
			Answer:     "请参考解析内容",
			RawContent: content,
		}, nil
	}

	// 验证JSON解析结果的有效性
	if strings.TrimSpace(result.Analysis) == "" && strings.TrimSpace(result.Answer) == "" {
		return nil, fmt.Errorf("DeepSeek返回的JSON内容为空")
	}

	return &DeepseekResult{
		Analysis:   result.Analysis,
		Answer:     result.Answer,
		RawContent: content,
	}, nil
}

// sendRequest 发送HTTP请求
func (s *AIService) sendRequest(apiURL, apiKey string, requestBody interface{}) ([]byte, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ HTTP请求发送失败: %v\n", err)
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 打印详细的响应信息用于调试
	fmt.Printf("📋 API响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("📋 API响应头: %+v\n", resp.Header)
	fmt.Printf("📋 API响应内容: %s\n", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ API请求失败，状态码: %d, 响应: %s\n", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// validateDeepSeekResponse 验证DeepSeek响应内容的有效性
func (s *AIService) validateDeepSeekResponse(content string) error {
	// 检查内容是否为空
	if strings.TrimSpace(content) == "" {
		return fmt.Errorf("响应内容为空")
	}

	// 检查内容长度是否过短
	if len(strings.TrimSpace(content)) < 5 {
		return fmt.Errorf("响应内容过短: %s", content)
	}

	// 检查是否包含明显的错误信息
	errorPatterns := []string{
		"error",
		"错误",
		"失败",
		"无法处理",
		"不支持",
		"sorry",
		"apologize",
		"can't",
		"cannot",
		"unable",
		"failed",
		"invalid",
		"bad request",
		"unauthorized",
		"forbidden",
		"not found",
		"internal server error",
		"service unavailable",
		"timeout",
		"rate limit",
	}

	contentLower := strings.ToLower(content)
	for _, pattern := range errorPatterns {
		if strings.Contains(contentLower, pattern) {
			return fmt.Errorf("响应包含错误信息: %s", content)
		}
	}

	// 检查是否为有效的JSON格式（如果是JSON）
	if strings.HasPrefix(strings.TrimSpace(content), "{") {
		var jsonData map[string]interface{}
		if err := json.Unmarshal([]byte(content), &jsonData); err != nil {
			return fmt.Errorf("JSON格式无效: %w", err)
		}

		// 检查JSON是否包含有效的分析或答案字段
		analysis, hasAnalysis := jsonData["analysis"]
		answer, hasAnswer := jsonData["answer"]

		if !hasAnalysis && !hasAnswer {
			return fmt.Errorf("JSON响应缺少必要的analysis或answer字段")
		}

		// 检查字段内容是否有效
		if hasAnalysis {
			if analysisStr, ok := analysis.(string); ok {
				if strings.TrimSpace(analysisStr) == "" {
					return fmt.Errorf("analysis字段为空")
				}
			}
		}

		if hasAnswer {
			if answerStr, ok := answer.(string); ok {
				if strings.TrimSpace(answerStr) == "" {
					return fmt.Errorf("answer字段为空")
				}
			}
		}
	}

	return nil
}

// ValidateImageURL 验证图片URL是否可访问
func (s *AIService) ValidateImageURL(imageURL string) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return fmt.Errorf("无法访问图片URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("无法确定文件类型")
	}

	validTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的图片格式: %s", contentType)
}

// parseQwenResponse 解析Qwen响应，支持中文和英文字段名
func (s *AIService) parseQwenResponse(content string) (model.QuestionStructure, error) {
	var structure model.QuestionStructure

	// 解析原始JSON数据
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &rawData); err != nil {
		// 如果JSON解析完全失败，作为纯文本处理
		fmt.Printf("⚠️ JSON解析失败，作为纯文本处理\n")
		return model.QuestionStructure{
			QuestionText: content,
			RawContent:   content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3, // 默认中等难度
			Content:      content, // 向后兼容
		}, nil
	}

	fmt.Printf("🔍 成功解析JSON，开始提取字段\n")

	// 提取题目类型（支持中英文字段名）
	if questionType, exists := rawData["question_type"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	} else if questionType, exists := rawData["类型"]; exists {
		if typeStr, ok := questionType.(string); ok {
			structure.QuestionType = typeStr
		}
	}

	// 提取题目内容（支持中英文字段名）
	if questionText, exists := rawData["question_text"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	} else if questionText, exists := rawData["题目"]; exists {
		if textStr, ok := questionText.(string); ok {
			structure.QuestionText = textStr
		}
	}

	// 提取选项
	structure.Options = make(map[string]string)

	// 标准选项格式 A, B, C, D, E, F
	optionKeys := []string{"A", "B", "C", "D", "E", "F"}
	for _, key := range optionKeys {
		if option, exists := rawData[key]; exists {
			if optionStr, ok := option.(string); ok && strings.TrimSpace(optionStr) != "" {
				structure.Options[key] = optionStr
				fmt.Printf("✅ 提取选项 %s='%s'\n", key, optionStr)
			}
		}
	}

	// 判断题的Y/N字段
	if yValue, exists := rawData["Y"]; exists {
		if yStr, ok := yValue.(string); ok && strings.TrimSpace(yStr) != "" {
			structure.Options["Y"] = yStr
			fmt.Printf("✅ 提取选项 Y='%s'\n", yStr)
		}
	}
	if nValue, exists := rawData["N"]; exists {
		if nStr, ok := nValue.(string); ok && strings.TrimSpace(nStr) != "" {
			structure.Options["N"] = nStr
			fmt.Printf("✅ 提取选项 N='%s'\n", nStr)
		}
	}

	fmt.Printf("🔍 提取到选项数量: %d\n", len(structure.Options))

	// 检查是否解析到有效数据
	if structure.QuestionText == "" && structure.QuestionType == "" {
		fmt.Printf("⚠️ 未解析到有效的题目数据\n")
		return model.QuestionStructure{
			QuestionText: content,
			RawContent:   content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3,
			Content:      content,
		}, nil
	}

	// 处理判断题的特殊字段映射（Y/N -> A/B）
	if strings.Contains(strings.ToLower(structure.QuestionType), "判断") {
		fmt.Printf("🔍 检测到判断题，处理Y/N字段映射\n")

		// 如果有Y/N字段，映射到A/B
		if yValue, hasY := structure.Options["Y"]; hasY {
			structure.Options["A"] = yValue
			fmt.Printf("✅ 映射 Y='%s' -> A='%s'\n", yValue, yValue)
		}
		if nValue, hasN := structure.Options["N"]; hasN {
			structure.Options["B"] = nValue
			fmt.Printf("✅ 映射 N='%s' -> B='%s'\n", nValue, nValue)
		}
	}

	// 设置默认值
	if structure.Subject == "" {
		structure.Subject = "未知"
	}
	if structure.Grade == "" {
		structure.Grade = "未知"
	}
	if structure.Difficulty == 0 {
		structure.Difficulty = 3 // 默认中等难度
	}

	// 确保向后兼容
	if structure.Content == "" && structure.QuestionText != "" {
		structure.Content = structure.QuestionText
	}
	if structure.RawContent == "" {
		structure.RawContent = content
	}

	fmt.Printf("✅ 成功解析Qwen响应\n")
	fmt.Printf("🔍 解析结果 - 类型: %s, 题目: %s, 选项数量: %d\n", structure.QuestionType, structure.QuestionText, len(structure.Options))

	return structure, nil
}


